import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import getConfig from "next/config";
import { RootState } from "reducers";
import { ArrowLeftOutlined, LoadingOutlined } from "@ant-design/icons";
import { loadStripe } from "@stripe/stripe-js";
import clsx from "clsx";

import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import useUserHook from "hooks/useUser";
import { fetchCreditPlan } from "actions/creditPlan";
import { ICredit } from "types/wallet";
import AppSpin from "components/AppSpin";
import AppModal from "components/AppModal";
import {
  DISCOUNT_TYPE,
  walletPath,
} from "components/StartAuthentication/constant";
import apiCore from "utils/apiCore";
import { parseError } from "utils/error";
import { showErrorPopupMessage } from "utils/message";

const { publicRuntimeConfig } = getConfig();
const { STRIPE_PUBLISHABLE_KEY } = publicRuntimeConfig;

const BuyTokenPlan = () => {
  const router = useRouter();
  const { id } = router.query;
  const { locale } = router;
  const dispatch = useAppDispatch();
  const { accessToken } = useUserHook();

  const { items, isFetchItemsLoading } = useAppSelector(
    (state: RootState) => state.creditPlan
  );

  const [selectedPlan, setSelectedPlan] = useState<ICredit | null>(null);
  const [discountCode, setDiscountCode] = useState("");
  const [discountPrice, setDiscountPrice] = useState<number | null>(null);
  const [isDiscountModalOpen, setIsDiscountModalOpen] = useState(false);
  const [actualSettlement, setActualSettlement] = useState(0);
  const [errorMsg, setErrorMsg] = useState("");
  const [isConfirmToApply, setIsConfirmToApply] = useState(false);
  const [isConfirmToPayLoading, setIsConfirmToPayLoading] = useState(false);

  useEffect(() => {
    if (accessToken && !items?.length) {
      dispatch(fetchCreditPlan({ accessToken }));
    }
  }, [dispatch, accessToken, items?.length]);

  useEffect(() => {
    if (items && id) {
      const plan = items.find(
        (item: ICredit) => item.id === parseInt(id as string)
      );
      if (plan) {
        setSelectedPlan(plan);
        const price = parseFloat(plan.price);
        setActualSettlement(price);
      }
    }
  }, [items, id]);

  useEffect(() => {
    if (isDiscountModalOpen) {
      setErrorMsg("");
      setDiscountCode("");
    }
  }, [isDiscountModalOpen]);

  const handleApplyDiscount = async () => {
    if (isConfirmToApply) return;

    try {
      setIsConfirmToApply(true);
      const res = await apiCore.get(
        null,
        "v1/credit_plan_discount",
        {
          credit_plan_discount_code: discountCode,
          currency: "USD",
          in_sale: selectedPlan?.in_sale,
        },
        accessToken
      );
      validateDiscount(res);
    } catch (error) {
      setErrorMsg(parseError(error).message);
    } finally {
      setIsConfirmToApply(false);
    }
  };

  const validateDiscount = (res: any) => {
    const currentTime = new Date().getTime();
    const validFromTime = new Date(res.valid_from).getTime();
    const expiredAtTime = new Date(res.expired_at).getTime();

    const isTimeValid =
      validFromTime < currentTime && expiredAtTime > currentTime;

    if (!isTimeValid) {
      if (validFromTime >= currentTime) {
        setErrorMsg("Discount code is not yet valid");
      } else if (expiredAtTime <= currentTime) {
        setErrorMsg("Discount code has expired");
      }
      return;
    }

    const originalPrice = parseFloat(selectedPlan?.price ?? "0");
    const discountValue = parseFloat(res.discount_value ?? "0");
    switch (res.type) {
      case DISCOUNT_TYPE.ABSOLUTE:
        setDiscountPrice(discountValue);
        setActualSettlement(originalPrice - discountValue);
        break;
      case DISCOUNT_TYPE.RELATIVE:
        setDiscountPrice(originalPrice * discountValue);
        setActualSettlement(originalPrice - originalPrice * discountValue);
        break;
    }

    setIsDiscountModalOpen(false);
  };

  const handleConfirmToPay = async () => {
    if (isConfirmToPayLoading) return;

    try {
      setIsConfirmToPayLoading(true);
      const result = await apiCore.post(
        null,
        "v1/credit_plan_order",
        {
          credit_plan_id: selectedPlan?.id,
          credit_plan_discount_code: discountCode || null,
          payment_method: "stripe_web",
          success_url: `${origin}/${locale}${walletPath}?status=success`,
          cancel_url: `${origin}/${locale}${walletPath}?status=cancel`,
        },
        accessToken
      );
      const stripe = await loadStripe(STRIPE_PUBLISHABLE_KEY);
      if (stripe) {
        const { error } = await stripe.redirectToCheckout({
          sessionId: result.stripe_checkout_session_id,
        });
        if (error) {
          showErrorPopupMessage(parseError(error).message);
        }
      }
    } catch (error) {
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setIsConfirmToPayLoading(false);
    }
  };

  if (isFetchItemsLoading || !selectedPlan) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <AppSpin />
      </div>
    );
  }

  return (
    <div className="max-w-screen-lg m-auto min-h-screen text-white p-4 md:p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => router.back()}
          className="flex items-center w-10 h-10"
        >
          <ArrowLeftOutlined className="text-white text-lg" />
        </button>
        <h1 className="text-xl md:text-2xl font-bold">Checkout</h1>
        <div className="w-10"></div>
      </div>

      {/* Content */}
      <div className="md:pt-16 pt-8">
        {/* Token Plan Info */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg md:text-xl font-bold">
              {parseFloat(selectedPlan.credit).toFixed(0)} $LEGIT Tokens
            </h2>
            <div className="text-lg md:text-xl font-bold">
              {parseFloat(selectedPlan.price).toFixed(2)} USD
            </div>
          </div>
          <p className="text-gray-100 text-sm md:text-base">
            LEGIT TOKEN for Legit authentication.
          </p>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-600 my-6"></div>

        {/* Subtotal */}
        <div className="flex justify-between items-center mb-6">
          <span className="text-base md:text-lg">Subtotal</span>
          <span className="text-base md:text-lg font-semibold">
            {parseFloat(selectedPlan.price).toFixed(2)} USD
          </span>
        </div>

        {/* Discount Code */}
        <div>
          <div className="flex justify-between items-center">
            <span className="text-base md:text-lg">Discount Code</span>
            <div>
              {discountPrice !== null ? (
                <div className="text-red-600 font-bold">
                  <span>-{discountPrice.toFixed(2)}</span>
                  <span className="ml-2">USD</span>
                </div>
              ) : (
                <button
                  onClick={() => setIsDiscountModalOpen(true)}
                  className="border-2 border-red-600 hover:opacity-80 text-red-600 px-4 py-1 rounded-full font-semibold"
                >
                  Enter
                </button>
              )}
            </div>
          </div>
          {discountPrice !== null && (
            <div
              className="text-gray-100 cursor-pointer md:text-sm text-xs mt-4"
              onClick={() => setDiscountPrice(null)}
            >
              remove
            </div>
          )}
        </div>

        {/* Divider */}
        <div className="border-t border-gray-600 my-6"></div>

        {/* Actual Settlement */}
        <div className="flex justify-between items-center mb-8">
          <span className="text-base md:text-lg font-semibold">
            Actual Settlement
          </span>
          <span className="text-base md:text-lg font-bold">
            {actualSettlement.toFixed(2)} USD
          </span>
        </div>
      </div>

      {/* Fixed Bottom Button */}
      <div className="fixed bottom-0 left-0 right-0 p-4 md:p-6 bg-black max-w-screen-lg m-auto">
        <button
          onClick={handleConfirmToPay}
          className={clsx(
            "w-full bg-btn-gradient hover:opacity-90 text-white font-bold py-4 rounded-lg text-lg md:text-xl transition-opacity",
            {
              "opacity-50 cursor-not-allowed": isConfirmToPayLoading,
            }
          )}
        >
          {isConfirmToPayLoading ? <LoadingOutlined /> : "CONFIRM TO PAY"}
        </button>
      </div>

      {/* Discount Code Modal */}
      <AppModal
        title="Discount Code"
        isModalOpen={isDiscountModalOpen}
        setIsModalOpen={setIsDiscountModalOpen}
      >
        <div className="text-white">
          <div className="text-center mb-6">
            <p className="text-gray-400 text-sm md:text-base">
              Promo codes cannot be used with other special offers.
            </p>
          </div>

          <div>
            <input
              type="text"
              value={discountCode}
              onChange={(e) => setDiscountCode(e.target.value)}
              placeholder="Enter Promo Code"
              className="outline-none w-full bg-transparent border border-gray-600 rounded-lg px-4 py-2 mb-2"
            />
          </div>

          <div className="text-red-500 h-6">{errorMsg}</div>

          <button
            onClick={handleApplyDiscount}
            className={clsx(
              "w-full bg-btn-gradient hover:opacity-90 text-white font-bold py-2 rounded-lg text-lg transition-opacity",
              {
                "cursor-not-allowed opacity-50": isConfirmToApply,
              }
            )}
          >
            {isConfirmToApply ? <LoadingOutlined /> : "APPLY"}
          </button>
        </div>
      </AppModal>
    </div>
  );
};

export default BuyTokenPlan;
