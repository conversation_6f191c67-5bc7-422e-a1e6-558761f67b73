import React, { useMemo } from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import { RootState } from "reducers";
import moment from "moment";
import { LeftOutlined } from "@ant-design/icons";

import useAppSelector from "hooks/useAppSelector";
import useOrderDetail from "hooks/useOrderDetail";
import { IServiceExtraService } from "types/orders";
import AppSpin from "components/AppSpin";
import DetailCardInfo from "components/StartAuthentication/OrdersComp/DetailCardInfo";
import AuthenticationResults from "components/StartAuthentication/OrdersComp/AuthenticationResults";
import MatchedResults from "components/StartAuthentication/OrdersComp/MatchedResults";
import NotResults from "components/StartAuthentication/OrdersComp/NotResults";
import AIPoweredReplicaScore from "components/StartAuthentication/OrdersComp/AIPoweredReplicaScore";
import { formatDate } from "utils/authHelper";
import { getLocalised<PERSON>ield } from "utils/locale";
import ReviewsFromAuthenticators from "components/StartAuthentication/OrdersComp/ReviewsFromAuthenticators";
import NFTCertificate from "components/StartAuthentication/OrdersComp/NFTCertificate";
import AuthenticatorMarkedPhotos from "components/StartAuthentication/OrdersComp/AuthenticatorMarkedPhotos";
import AdditionalPhotos from "components/StartAuthentication/OrdersComp/AdditionalPhotos";
import {
  CATEGORY,
  getOrdersConversationPath,
  SERVICE_REQUEST_ADDITIONAL_STATUS,
  SERVICE_REQUEST_IMAGE_TYPE,
} from "components/StartAuthentication/constant";
import GalleryImage from "components/GalleryImage";
import OrderDetailSectionContainer from "components/StartAuthentication/OrdersComp/OrderDetailSectionContainer";
import RequestLog from "components/StartAuthentication/OrdersComp/RequestLog";

const OrderDetail = () => {
  const router = useRouter();
  const { locale = "", defaultLocale = "en" } = router;
  const { id } = router.query;
  const { accessToken } = useAppSelector((state: RootState) => state.app);
  const { orderDetail, isLoading } = useOrderDetail({
    id: id as string,
    accessToken,
  });

  const {
    product_brand,
    product_model,
    service_request_image,
    service_request_additional,
    uuid,
    result,
    category_id,
    created_at,
    completed_at,
    service_request_result,
    service_level_minute,
    user_remark,
    status,
    product_category,
    fake_rating,
    service_extra_service_insurance_purchased,
    service_extra_service_nft_certificate_purchased,
    nft_certificate_minted,
    nft_certificate_minted_at,
    service_extra_service_snapshot,
    legit_tag_uuid,
    service_request_log,
  } = orderDetail || {};

  const serviceExtraServiceSnapshot = useMemo(() => {
    try {
      return JSON.parse(
        service_extra_service_snapshot || "[]"
      ) as IServiceExtraService[];
    } catch (error) {
      return [];
    }
  }, [service_extra_service_snapshot]);

  const NFTCertificateSnapshot = useMemo(() => {
    return (
      serviceExtraServiceSnapshot.find((item) => !!item.is_nft_certificate) ||
      null
    );
  }, [serviceExtraServiceSnapshot]);

  const insuranceSnapshot = useMemo(() => {
    return (
      serviceExtraServiceSnapshot.find((item) => !!item.is_insurance) || null
    );
  }, [serviceExtraServiceSnapshot]);

  const isCertReady = useMemo(() => {
    return (
      nft_certificate_minted === 1 &&
      moment().isAfter(moment(nft_certificate_minted_at ?? moment()))
    );
  }, [nft_certificate_minted, nft_certificate_minted_at]);

  const markerImageUrl = useMemo(() => {
    return (
      service_request_image
        ?.filter((item) => !!item.marker_image_url)
        .map((images) => images.marker_image_url) || []
    );
  }, [service_request_image]);

  const uploadedPhotosList = useMemo(
    () =>
      service_request_image?.filter(
        (item) => item.type === SERVICE_REQUEST_IMAGE_TYPE.INITIAL
      ) || [],
    [service_request_image]
  );

  const serviceRequestAdditionalPending = useMemo(
    () =>
      service_request_additional?.filter(
        (item) => item.status === SERVICE_REQUEST_ADDITIONAL_STATUS.PENDING
      ) || [],
    [service_request_additional]
  );

  const serviceRequestAdditionalCompleted = useMemo(
    () =>
      service_request_additional?.filter(
        (item) => item.status === SERVICE_REQUEST_ADDITIONAL_STATUS.COMPLETED
      ) || [],
    [service_request_additional]
  );

  const TagCategory = useMemo(() => {
    return category_id === 1 ? "KICKS TAG" : "LUXE TAG";
  }, [category_id]);

  const { icon_image_url: brandIconUrl, title: brandTitle } =
    product_brand || {};

  const { icon_image_url: modelIconUrl, title: modelTitle } =
    product_model || {};

  if (isLoading) {
    return <AppSpin />;
  }

  if (!isLoading && !orderDetail) {
    return <div>Empty</div>;
  }

  return (
    <div className="max-w-screen-lg m-auto text-white py-8">
      {/* header */}
      <div className="md:px-[44px] px-4 flex justify-between items-center mb-24">
        <div className="cursor-pointer" onClick={() => router.back()}>
          <LeftOutlined style={{ fontSize: "24px" }} />
        </div>
        <div>#{uuid}</div>
        <div></div>
      </div>
      {/* product info */}
      <OrderDetailSectionContainer>
        <DetailCardInfo
          brandIconUrl={brandIconUrl}
          modelIconUrl={modelIconUrl}
          brandTitle={brandTitle}
          modelTitle={modelTitle}
          status={status}
          result={result}
          categoryId={category_id}
          createdAt={created_at}
          serviceLevelMinute={service_level_minute}
          completedAt={completed_at}
          uuid={uuid}
        />
      </OrderDetailSectionContainer>

      {legit_tag_uuid && (
        <OrderDetailSectionContainer title={`Your ${TagCategory} Code`}>
          <div className="w-full sm:h-36 rounded-lg overflow-hidden relative">
            <img
              src={
                category_id === 1
                  ? "/order/bg_kicks_tag_banner.png"
                  : "/order/bg_luxe_tag_banner.png"
              }
              className="w-full h-full object-cover"
              alt="bg_tag"
            />
            <div className="absolute inset-0 flex items-center">
              <div className="px-4 sm:px-6 sm:space-y-2 text-white/80">
                <div className="text-sm sm:text-base">Authenticate with</div>
                <div className="text-base sm:text-xl font-bold text-white">
                  {TagCategory}
                </div>
                <div className="text-sm sm:text-base">#{legit_tag_uuid}</div>
              </div>
            </div>
          </div>
        </OrderDetailSectionContainer>
      )}

      {/* Uploaded Photos */}
      <OrderDetailSectionContainer title="Uploaded Photos">
        <GalleryImage imageList={uploadedPhotosList || []} />
      </OrderDetailSectionContainer>

      {/* User Remarks */}
      {user_remark && (
        <OrderDetailSectionContainer title="User Remarks">
          <div>{user_remark}</div>
        </OrderDetailSectionContainer>
      )}

      {status !== SERVICE_REQUEST_ADDITIONAL_STATUS.CANCELLED && (
        <>
          {/* Additional Photos - completed */}
          {serviceRequestAdditionalCompleted &&
            serviceRequestAdditionalCompleted.length > 0 &&
            serviceRequestAdditionalCompleted.map((additional) => (
              <OrderDetailSectionContainer
                key={additional.id}
                title="Additional Photos"
                rightContent={
                  <div className="text-right text-xs text-gray-100">
                    <div className="capitalize">{additional.status}</div>
                    <div>
                      {moment(additional.created_at).format(
                        "DD MMM YYYY, h:mm A"
                      )}
                    </div>
                  </div>
                }
              >
                <GalleryImage
                  imageList={
                    service_request_image?.filter(
                      (image) =>
                        image.service_request_additional_id === additional.id
                    ) || []
                  }
                />
              </OrderDetailSectionContainer>
            ))}

          {/* Additional Photos - pending */}
          {serviceRequestAdditionalPending &&
            serviceRequestAdditionalPending.length > 0 &&
            serviceRequestAdditionalPending.map((item) => (
              <OrderDetailSectionContainer key={item.id} title="">
                <AdditionalPhotos
                  id={id as string}
                  accessToken={accessToken}
                  serviceRequestAdditionalPending={
                    serviceRequestAdditionalPending
                  }
                />
              </OrderDetailSectionContainer>
            ))}
        </>
      )}

      {/* Authentication Results */}
      <OrderDetailSectionContainer
        title="Authentication Results"
        isNeedBorder={false}
      >
        {result ? (
          category_id === CATEGORY.CODE_CHECKING ? (
            <MatchedResults
              result={result}
              uuid={uuid}
              completedAt={completed_at}
              categoryId={category_id}
            />
          ) : (
            <AuthenticationResults
              result={result}
              categoryId={category_id}
              uuid={uuid}
              completedAt={completed_at}
              service_request_result={service_request_result}
            />
          )
        ) : (
          <NotResults
            status={status}
            uuid={uuid}
            imgUrl={product_category?.icon_image_url}
          />
        )}
      </OrderDetailSectionContainer>

      {/* AI-Powered Replica Score */}
      {fake_rating && (
        <OrderDetailSectionContainer
          title="AI-Powered Replica Score"
          isNeedBorder={false}
        >
          <AIPoweredReplicaScore fake_rating={fake_rating} />
        </OrderDetailSectionContainer>
      )}

      {/* Check conversation records */}
      <OrderDetailSectionContainer>
        <div className="flex justify-center items-center">
          <Link
            href={`${getOrdersConversationPath(id as string)}`}
            className="bg-dark-100 rounded-full py-2 px-6 mx-auto space-x-8 inline-flex justify-between items-center "
          >
            <span className="text-gray-100 text-sm">
              Check conversation records
            </span>
            <span className="w-6 h-6 md:w-8 md:h-8">
              <img
                src="/order/icon_msg_send.png"
                className="w-full h-full"
                alt="msg send icon"
              />
            </span>
          </Link>
        </div>
      </OrderDetailSectionContainer>

      {/* NFT Certificate */}
      {!!service_extra_service_nft_certificate_purchased &&
        NFTCertificateSnapshot && (
          <OrderDetailSectionContainer
            title={getLocalisedField(NFTCertificateSnapshot, "title", locale)}
            rightContent={
              <div className="text-white text-xs">
                <div className="text-right">
                  {nft_certificate_minted === 0 ? "Processing" : "Ready"}
                </div>
                {nft_certificate_minted_at && (
                  <div>
                    {formatDate(
                      locale,
                      defaultLocale,
                      nft_certificate_minted_at
                    )}
                  </div>
                )}
              </div>
            }
          >
            <NFTCertificate
              uuid={uuid}
              locale={locale}
              NFTCertificateSnapshot={NFTCertificateSnapshot}
              isCertReady={isCertReady}
            />
          </OrderDetailSectionContainer>
        )}
      {/* Financial Guarantee */}
      {!!service_extra_service_insurance_purchased && insuranceSnapshot && (
        <OrderDetailSectionContainer
          title={getLocalisedField(insuranceSnapshot, "title", locale)}
        >
          <div className="text-gray-100 md:text-sm text-xs">
            {getLocalisedField(insuranceSnapshot, "description", locale)}
          </div>
        </OrderDetailSectionContainer>
      )}
      {/* Reviews from Authenticators */}
      {!!service_request_result?.length && (
        <OrderDetailSectionContainer title="Reviews from Authenticators">
          <ReviewsFromAuthenticators
            serviceRequestResult={service_request_result}
            locale={locale}
            defaultLocale={defaultLocale}
          />
        </OrderDetailSectionContainer>
      )}

      {/* Authenticator Marked Photos */}
      {!!markerImageUrl.length && (
        <OrderDetailSectionContainer title="Authenticator Marked Photos">
          <AuthenticatorMarkedPhotos markerImageUrl={markerImageUrl} />
        </OrderDetailSectionContainer>
      )}

      {/* GET HELP */}
      <OrderDetailSectionContainer>
        <div className="md:max-w-md m-auto mt-6">
          <button className="w-full border-2 border-white p-2 text-center">
            GET HELP
          </button>
        </div>
      </OrderDetailSectionContainer>

      {/* service_request_log */}
      {service_request_log && service_request_log.length > 0 && (
        <OrderDetailSectionContainer title="Request Log" isNeedBorder={false}>
          <RequestLog serviceRequestLog={service_request_log} />
        </OrderDetailSectionContainer>
      )}
    </div>
  );
};

export default OrderDetail;
