import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

import AppSpin from "components/AppSpin";
import NavigationHeader from "components/NavigationHeader";
import AuthenticationService from "components/StartAuthentication/new-authentication/AuthenticationService";
import Certificate from "components/StartAuthentication/new-authentication/Certificate";
import CertificateOwner from "components/StartAuthentication/new-authentication/CertificateOwner";
import {
  IServicePlaceholder,
  IServiceSet,
  IUploadPhotoItem,
} from "types/orders";
import apiCore from "utils/apiCore";
import PhotosList from "components/StartAuthentication/new-authentication/PhotosList";
import CaseCustomCode from "components/StartAuthentication/new-authentication/CaseCustomCode";
import NotesToOurAuthenticators from "components/StartAuthentication/new-authentication/NotesToOurAuthenticators";
import AdditionalServices from "components/StartAuthentication/new-authentication/AdditionalServices";
import Link from "next/link";
import BalanceInfo from "components/StartAuthentication/BalanceInfo";
import Introduction from "components/StartAuthentication/new-authentication/Introduction";
import Instructions from "components/StartAuthentication/new-authentication/Instructions";
import useNewAuthenticationForm from "hooks/useNewAuthenticationForm";
import { showErrorPopupMessage } from "utils/message";
import useUserHook from "hooks/useUser";
import { checkoutPath } from "components/StartAuthentication/constant";

const NewAuthentication = () => {
  const router = useRouter();
  const { locale } = router;
  const { id } = router.query;

  const { accessToken, user } = useUserHook();

  const [categoryId, brandId, modelId] = (id as string[]) || [];

  const [isLoading, setIsLoading] = useState(false);

  const {
    serviceSet,
    setServiceSet,
    setCurrentServiceLevel,
    setCurrentAdditionalServiceList,
    validateRequiredPhotos,
    validateBalance,
    setCategoryBrandModel,
    setUploadPhotoList,
  } = useNewAuthenticationForm();

  useEffect(() => {
    const fetchServiceSet = async () => {
      setIsLoading(true);
      try {
        const res: IServiceSet = await apiCore.get(
          null,
          `v3/service_set`,
          {
            category_id: categoryId,
            brand_id: brandId,
            model_id: modelId,
          },
          accessToken
        );
        setServiceSet(res);
        if (res?.service_extra_service?.[1]) {
          setCurrentAdditionalServiceList([res?.service_extra_service?.[1]]);
        }
        setCurrentServiceLevel(res?.service_level?.[0]);
      } finally {
        setIsLoading(false);
      }
    };

    if (categoryId && brandId && modelId) {
      setCategoryBrandModel({
        category_id: categoryId,
        brand_id: brandId,
        model_id: modelId,
      });
      if (accessToken) {
        fetchServiceSet();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryId, brandId, modelId, accessToken]);

  const handleSubmit = async () => {
    if (!accessToken) {
      showErrorPopupMessage("Please login to continue");
      return;
    }

    const balanceValidation = validateBalance(user);
    if (!balanceValidation.isValid) {
      showErrorPopupMessage(balanceValidation.message);
      return;
    }

    const validation = validateRequiredPhotos();
    if (!validation.isValid) {
      const missingCount = validation.missingPhotos.length;
      showErrorPopupMessage(
        `Please upload all required photos. ${missingCount} photo${
          missingCount > 1 ? "s" : ""
        } still missing.`
      );
      return;
    }

    router.push(`${checkoutPath}`);
  };

  return (
    <div className="text-white">
      <NavigationHeader progress="80%" />

      <div className="max-w-screen-lg m-auto md:py-10 py-4 md:px-12 px-4">
        <div className="md:text-3xl text-xl font-bold md:mb-10 mb-4">
          New Authentication
        </div>

        {/* loading */}
        {isLoading && <AppSpin />}

        {!isLoading && (
          <div className="flex flex-col md:gap-8 gap-4">
            {/* introduction */}
            <Introduction
              locale={locale || ""}
              categoryId={categoryId}
              brandId={brandId}
              modelId={modelId}
            />
            {/* balance */}
            <BalanceInfo isShowBuyBtn={true} user={user} />
            {/* authentication service */}
            <AuthenticationService
              locale={locale || ""}
              service_level={serviceSet?.service_level}
            />
            {/* LEGIT APP Certificate */}
            <Certificate />
            {/* LEGIT APP Certificate Owner */}
            <CertificateOwner
              ownerName={user?.last_certificate_owner_name || user?.name}
            />

            <Instructions
              guidelineList={serviceSet?.service_guideline || []}
              locale={locale || ""}
            />
            {/* Required Photos */}
            <PhotosList
              list={serviceSet?.service_placeholder?.filter(
                (item: IServicePlaceholder) => item.required === 1
              )}
              title={"Required Photos"}
              locale={locale || "en"}
              setUploadPhotoList={setUploadPhotoList}
            />
            {/* Optional photos */}
            <PhotosList
              list={serviceSet?.service_placeholder?.filter(
                (item: IServicePlaceholder) => item.required === 0
              )}
              title={"Optional photos"}
              locale={locale || "en"}
              isOptional={true}
              setUploadPhotoList={setUploadPhotoList}
            />
            {/* Case Custom Code */}
            <CaseCustomCode />
            {/* NotesToOurAuthenticators */}
            <NotesToOurAuthenticators />
            {/* Additional Services */}
            <AdditionalServices
              locale={locale || ""}
              extraServiceList={serviceSet?.service_extra_service}
            />
            {/* continue */}
            <div>
              <div
                onClick={handleSubmit}
                className="text-center md:text-xl cursor-pointer bg-gradient-red font-bold md:py-6 py-2 rounded-lg"
              >
                Continue
              </div>
            </div>
            <div className="text-gray-500 text-center md:text-base text-xs">
              By tapping “Continue”, you agree to our{" "}
              <Link href="/terms" className="underline" target="_blank">
                Terms of Service
              </Link>
              ，{" "}
              <Link href="/privacy" className="underline" target="_blank">
                Privacy Policy
              </Link>
              ，and{" "}
              <Link
                href="/financial-guarantee"
                className="underline"
                target="_blank"
              >
                Financial Guarantee Policy
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewAuthentication;
