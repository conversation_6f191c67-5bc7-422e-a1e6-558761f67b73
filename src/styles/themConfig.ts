// theme/themeConfig.ts
import type { ThemeConfig } from "antd";

const theme: ThemeConfig = {
  token: {
    // fontSize: 16,
    // colorPrimary: '#52c41a',
    fontFamily: "Montserrat",
  },
  components: {
    Select: {
      /* here is your component tokens */
      selectorBg: `#000`,
    },
    Modal: {
      contentBg: `#19191C`,
      footerBg: `#19191C`,
      headerBg: `#19191C`,
      titleColor: `#fff`,
    },
  },
};

export default theme;
