import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";
import clsx from "clsx";

import { getAuthStatus, getResultBgColor } from "utils/authHelper";
import { RESULT_TYPE } from "../constant";

const AuthOrReplicaCard = ({
  status,
  result,
  categoryId,
  isShowIcon = true,
  className = "",
}: {
  status?: string;
  result: "pass" | "not_pass";
  categoryId?: number;
  isShowIcon?: boolean;
  className?: string;
}) => {
  const intl = useIntl();
  return (
    <div
      className={clsx(
        "py-1 px-2 flex gap-2 items-center rounded-md font-bold",
        getResultBgColor(result),
        className
      )}
    >
      {isShowIcon && (
        <Image
          src={result === RESULT_TYPE.PASS ? "/Authentic.png" : "/replica.png"}
          width={30}
          height={13}
          alt={result === RESULT_TYPE.PASS ? "AUTHENTIC" : "REPLICA"}
          className="sm:w-[30px] sm:h-[13px] w-[20px] h-[8px]"
        />
      )}

      <div className="text-xs md:text-sm">
        {intl.formatMessage({
          id: getAuthStatus({
            status,
            categoryId,
            result,
          }),
        })}
      </div>
    </div>
  );
};

export default AuthOrReplicaCard;
