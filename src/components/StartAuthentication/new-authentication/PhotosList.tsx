import React, { useRef, useState } from "react";
import { IServicePlaceholder, IUploadPhotoItem } from "types/orders";
import { RootState } from "reducers";

import HeaderTitle from "./HeaderTitle";
import { getLocalisedField } from "utils/locale";
import Image from "next/image";
import apiCore from "utils/apiCore";
import AppSpin from "components/AppSpin";
import useAppSelector from "hooks/useAppSelector";

const PhotoCard = ({
  item,
  locale,
  setUploadPhotoList,
}: {
  item: IServicePlaceholder;
  locale: string;
  setUploadPhotoList: (
    listOrUpdater:
      | IUploadPhotoItem[]
      | ((prevList: IUploadPhotoItem[]) => IUploadPhotoItem[])
  ) => void;
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadedImage, setUploadedImage] = useState<{
    url: string;
  } | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const { accessToken } = useAppSelector((state: RootState) => state.app);
  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setIsUploading(true);
      try {
        const res = await apiCore.post(
          null,
          "v1/asset_image",
          file,
          accessToken
        );
        if (res && res.url) {
          setUploadedImage({
            url: res.url,
          });
          const newPhoto: IUploadPhotoItem = {
            image_url: res.url,
            source: "album",
            system_image_remark: {
              index: item.index,
              service_placeholder_id: item.id,
            },
          };

          setUploadPhotoList((prevList: IUploadPhotoItem[]) => {
            const existingIndex = prevList.findIndex(
              (photo: IUploadPhotoItem) =>
                photo.system_image_remark.service_placeholder_id === item.id
            );

            if (existingIndex !== -1) {
              const newList = [...prevList];
              newList[existingIndex] = newPhoto;
              return newList;
            } else {
              return [...prevList, newPhoto];
            }
          });
        }
      } catch (error) {
        console.error("Error uploading image:", error);
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleClick = () => {
    if (fileInputRef.current && !isUploading) {
      fileInputRef.current.click();
    }
  };

  return (
    <div
      className={`${
        isUploading ? "cursor-not-allowed" : "cursor-pointer"
      } flex flex-col items-center md:gap-4 gap-1`}
      onClick={handleClick}
    >
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileUpload}
        accept="image/*"
        style={{ display: "none" }}
      />
      <div
        className="w-full border border-gray-200 rounded-xl flex items-center justify-center relative"
        style={
          uploadedImage
            ? {
                backgroundImage: `url(${uploadedImage.url})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: "no-repeat",
              }
            : {}
        }
      >
        <Image src={item.image_url} width={128} height={128} alt={item.title} />

        {isUploading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-xl flex items-center justify-center">
            <AppSpin />
          </div>
        )}

        {uploadedImage && (
          <div className="absolute top-2 right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
            <svg
              className="w-4 h-4 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        )}
      </div>
      <div className="md:text-base text-[10px] text-center">
        {getLocalisedField(item, "title", locale)}
      </div>
    </div>
  );
};

const PhotosList = ({
  title,
  list = [],
  locale,
  isOptional = false,
  setUploadPhotoList,
}: {
  title: string;
  list: IServicePlaceholder[] | undefined;
  locale: string;
  isOptional?: boolean;
  setUploadPhotoList: (
    listOrUpdater:
      | IUploadPhotoItem[]
      | ((prevList: IUploadPhotoItem[]) => IUploadPhotoItem[])
  ) => void;
}) => {
  return (
    <div>
      <HeaderTitle title={title} />
      <div className="grid grid-cols-4 md:gap-4 gap-2">
        {list.map((item) => (
          <PhotoCard
            key={item.id}
            item={item}
            locale={locale}
            setUploadPhotoList={setUploadPhotoList}
          />
        ))}
        {isOptional && (
          <PhotoCard
            item={
              {
                id: -1,
                index: -1,
                title: "Additional",
                title_tc: "額外",
                title_sc: "额外",
                image_url: "/additional.png",
              } as IServicePlaceholder
            }
            setUploadPhotoList={setUploadPhotoList}
            locale={locale}
          />
        )}
      </div>
    </div>
  );
};

export default PhotosList;
