import React from "react";
import Link from "next/link";

import { IServiceExtraService } from "types/orders";
import HeaderTitle from "./HeaderTitle";
import { getLocalisedField } from "utils/locale";
import clsx from "clsx";
import Image from "next/image";
import useNewAuthenticationForm from "hooks/useNewAuthenticationForm";

const AdditionalServices = ({
  locale,
  extraServiceList,
}: {
  locale: string;
  extraServiceList: IServiceExtraService[] | undefined;
}) => {
  const { setCurrentAdditionalServiceList, currentAdditionalServiceList } =
    useNewAuthenticationForm();
  const handleServiceClick = (service: IServiceExtraService) => {
    if (
      currentAdditionalServiceList?.find(
        (item: IServiceExtraService) => item.id === service.id
      )
    ) {
      setCurrentAdditionalServiceList(
        currentAdditionalServiceList?.filter(
          (item: IServiceExtraService) => item.id !== service.id
        )
      );
    } else {
      setCurrentAdditionalServiceList([
        ...(currentAdditionalServiceList || []),
        service,
      ]);
    }
  };
  return (
    <div>
      <HeaderTitle title="Additional Services" />
      <div className="flex flex-col gap-4">
        {extraServiceList?.map((service) => {
          const isSelected =
            currentAdditionalServiceList?.some(
              (item: IServiceExtraService) => item.id === service.id
            ) || false;
          return (
            <div key={service.id} className="flex flex-col gap-4">
              <div
                onClick={() => handleServiceClick(service)}
                className={clsx(
                  "cursor-pointer text-xl flex flex-col gap-2 justify-center items-center w-full py-6 border rounded-[0.25rem]",
                  {
                    "border-red-500": isSelected,
                    "opacity-50 border-gray-100": !isSelected,
                  }
                )}
              >
                <div className="md:text-base text-sm">
                  {getLocalisedField(service, "title", locale)}
                </div>
                <div className="flex gap-2 items-center">
                  <div className="md:w-6 md:h-6 w-4 h-4">
                    <Image
                      src="/token.svg"
                      width={24}
                      height={24}
                      alt="token"
                    />
                  </div>
                  <div className="md:text-base text-sm">
                    {Number(service.credit || 0)
                      ? Number(service.credit).toFixed(2) + " $LEGIT Token"
                      : "Free"}
                  </div>
                </div>
              </div>
              <div className="flex justify-between items-center text-gray-500 md:text-base text-xs gap-2">
                <div className="md:text-base text-xs">
                  {getLocalisedField(service, "description", locale)}
                </div>
                {service.example_url_text && (
                  <Link
                    className="cursor-pointer text-right underline flex items-center whitespace-nowrap"
                    href={service.example_url || ""}
                    target="_blank"
                  >
                    {getLocalisedField(service, "example_url_text", locale)}
                    &nbsp;&nbsp;
                    <div>&gt;</div>
                  </Link>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AdditionalServices;
