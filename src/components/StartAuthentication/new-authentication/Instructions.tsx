import React, { useState } from "react";
import { IServiceGuideline } from "types/orders";
import ViewPhotoInstructionModal from "./ViewPhotoInstructionModal";

const Instructions = ({
  guidelineList,
  locale,
}: {
  guidelineList: IServiceGuideline[];
  locale: string;
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <div className="flex flex-col md:gap-5 gap-3">
        <div
          className="md:text-base text-xs w-full text-center underline text-gray-500 cursor-pointer"
          onClick={() => {
            window.open("https://legitapp.com/cert/6809939146413702");
          }}
        >
          View Certificate Example
        </div>
        <div
          onClick={() => {
            setIsModalOpen(true);
          }}
          className="cursor-pointer bg-gradient-red md:py-6 py-2 rounded-md text-center md:text-xl text-sm"
        >
          VIEW PHOTO INSTRUCTION
        </div>
      </div>
      <ViewPhotoInstructionModal
        setIsModalOpen={setIsModalOpen}
        isModalOpen={isModalOpen}
        guidelineList={guidelineList}
        locale={locale}
      />
    </>
  );
};

export default Instructions;
