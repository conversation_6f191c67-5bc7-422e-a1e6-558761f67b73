import React from "react";
import moment from "moment";
import { IServiceRequestMessage } from "types/orders";
import clsx from "clsx";

interface ConversationMessagesProps {
  messages: Partial<IServiceRequestMessage>[];
  locale: string;
  defaultLocale: string;
  user: any;
}

const ADMIN_ID = 28;
const ConversationMessages = ({
  messages,
  locale,
  defaultLocale,
  user,
}: ConversationMessagesProps) => {
  if (!messages || messages.length === 0) {
    return;
  }

  const groupedMessages = [...messages]
    .reverse()
    .reduce(
      (
        groups: { [key: string]: Partial<IServiceRequestMessage>[] },
        message
      ) => {
        const date = moment(message.created_at).format("YYYY-MM-DD");
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(message);
        return groups;
      },
      {}
    );

  const renderNameInfo = ({
    isUserMessage,
    message,
    user,
  }: {
    isUserMessage: boolean;
    message: Partial<IServiceRequestMessage>;
    user: any;
  }) => {
    if (isUserMessage) return `${user?.name}`;

    if (message?.checker_id === ADMIN_ID) {
      return `${message.checker?.name} (#${message.checker_id}) `;
    } else {
      return "LEGIT APP";
    }
  };
  return (
    <div className="space-y-4">
      {Object.entries(groupedMessages).map(([date, dayMessages]) => (
        <div key={date}>
          <div className="flex items-center justify-center mb-4">
            <div className="px-3 py-1 text-gray-500 font-semibold text-sm">
              {moment(date).format(
                locale === defaultLocale ? "D MMMM YYYY" : "YYYY年MM月DD日"
              )}
            </div>
          </div>

          <div className="space-y-4">
            {dayMessages.map((message) => {
              const isUserMessage = message.user_id === user?.id;

              return (
                <div
                  key={message.id}
                  className={clsx(
                    "flex items-start gap-3",
                    isUserMessage && "flex-row-reverse"
                  )}
                >
                  <div className="w-8 h-8 md:w-10 md:h-10 flex-shrink-0">
                    <img
                      src={
                        (isUserMessage
                          ? user.profile_image_url
                          : message?.checker?.profile_image_url) ||
                        "/order/icon_detail_result_overlay.png"
                      }
                      width={40}
                      height={40}
                      alt="avatar"
                      className="w-full h-full rounded-full object-cover"
                    />
                  </div>

                  {/* Message content */}
                  <div className="min-w-0 max-w-[80%] flex flex-col">
                    <div
                      className={clsx(
                        "flex items-center gap-2 mb-1",
                        isUserMessage ? "justify-end" : "justify-start"
                      )}
                    >
                      <div className="text-gray-500 font-semibold text-sm">
                        {renderNameInfo({ isUserMessage, message, user })}
                      </div>
                      <div className="text-gray-100 text-[10px]">
                        {moment(message.created_at).format("h:mm A")}
                      </div>
                    </div>

                    <div
                      className={clsx(
                        "text-sm bg-dark-100 text-white rounded-2xl px-4 py-3 inline-block w-fit",
                        isUserMessage ? "self-end" : "self-start"
                      )}
                    >
                      {message.content}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ConversationMessages;
