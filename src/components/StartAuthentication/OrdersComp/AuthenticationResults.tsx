import React from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";
import Link from "next/link";
import clsx from "clsx";
import {
  getAuthStatus,
  formatDate,
  getAuthResultDesc,
  getResultTextColor,
} from "utils/authHelper";
import { PATH_ROUTE } from "constants/app";
import { IServiceRequestResult } from "types/orders";

interface AuthenticationResultsProps {
  result?: "pass" | "not_pass";
  categoryId?: number;
  uuid?: string;
  completedAt?: string;
  user_remark?: string | null;
  service_request_result?: IServiceRequestResult[];
}
const AuthenticationResults = ({
  result,
  categoryId,
  uuid,
  completedAt,
  service_request_result,
}: AuthenticationResultsProps) => {
  const intl = useIntl();
  const router = useRouter();
  const { locale = "", defaultLocale = "" } = router;

  return (
    <div className="flex flex-col items-center justify-center gap-2">
      {/* logo */}
      <div className="w-8 md:w-10 mb-3">
        <img
          src="/order/icon_authentic.png"
          className="w-full h-auto object-contain"
          alt="Authentication logo"
        />
      </div>

      {/* Result Status */}
      <div className={clsx("font-bold", getResultTextColor(result || ""))}>
        {intl.formatMessage({ id: getAuthStatus({ result, categoryId }) })}
      </div>

      {/* Learn About Standards Button */}
      <Link
        href={PATH_ROUTE.STANDARDS}
        target="_blank"
        className="border border-gray-100 text-gray-400 px-4 py-1 rounded-full flex items-center gap-2 hover:border-white hover:text-white transition-colors mb-3"
      >
        <span className="w-4 h-4 border border-current rounded-full flex items-center justify-center text-xs sm:text-sm ">
          ?
        </span>
        <span className="text-xs sm:text-sm">Learn About Our Standards</span>
      </Link>

      {/* Order Details */}
      {uuid && <div className="text-gray-100 text-xs sm:text-sm ">#{uuid}</div>}

      {/* Completion Date */}
      {completedAt && (
        <div className="text-gray-100 text-xs sm:text-sm">
          Completed at: {formatDate(locale, defaultLocale, completedAt)}
        </div>
      )}

      {/* Checker Information */}
      {!!service_request_result?.length && (
        <div className="flex gap-2 text-gray-100 text-xs sm:text-sm ">
          <div>Checked by :</div>
          {service_request_result?.map((result, index) => (
            <span key={result.id}>
              {result.checker.name}(#{result.checker_id})
              {index < service_request_result.length - 1 && ", "}
            </span>
          ))}
        </div>
      )}

      {/* Authentication Description */}
      <div className="bg-dark-100 rounded-lg md:p-4 p-2 md:max-w-md mx-auto">
        <div className="text-gray-100 text-xs sm:text-sm ">
          {intl.formatMessage({
            id: getAuthResultDesc({ categoryId, result }),
          })}
        </div>
      </div>

      {/* Get Certificate Button */}
      <Link
        href={`${PATH_ROUTE.CERT}/${uuid}`}
        target="_blank"
        className="w-full md:max-w-md bg-gradient-blue text-white px-8 rounded-lg font-semibold text-xs sm:text-sm flex items-center justify-center hover:opacity-90 transition-opacity mt-4"
      >
        <span className="md:w-12 md:h-12 w-10 h-10">
          <img src="/order/icon_legal_information.png" alt="Certificate icon" />
        </span>
        GET CERTIFICATE FOR FREE
      </Link>
    </div>
  );
};

export default AuthenticationResults;
