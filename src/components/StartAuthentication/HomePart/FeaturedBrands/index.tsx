import Image from "next/image";
import React from "react";
import { useIntl } from "react-intl";

import { IAppBrand } from "types/app";

const FeaturedBrands = ({
  brandList,
  onClick,
}: {
  brandList: IAppBrand[];
  onClick: (brand: IAppBrand) => void;
}) => {
  const intl = useIntl();

  return (
    <div className="flex flex-col gap-4">
      <div className="font-bold md:text-2xl text-xl">
        {intl.formatMessage({
          id: "start_authentication_page_featured_brands",
        })}
      </div>
      <div className="flex gap-4 overflow-x-auto whitespace-nowrap hide-scrollbar">
        {brandList.map((brand) => (
          <div key={brand.id} onClick={() => onClick(brand)}>
            <div className="w-24 h-28 md:w-36 md:h-40 border border-1 border-gray-200 rounded-lg flex flex-col justify-center items-center cursor-pointer">
              <Image
                src={brand.icon_image_url}
                alt={brand.title}
                width={100}
                height={100}
                className="w-20 h-20 md:w-24 md:h-24"
              />
              <div className="md:text-base text-xs">{brand.title}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FeaturedBrands;
